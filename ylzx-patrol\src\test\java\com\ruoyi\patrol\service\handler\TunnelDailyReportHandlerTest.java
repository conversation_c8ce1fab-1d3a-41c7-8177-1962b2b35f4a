package com.ruoyi.patrol.service.handler;

import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import com.ruoyi.patrol.service.handler.impl.TunnelDailyReportHandler;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TunnelDailyReportHandler的测试类，特别测试列宽设置
 */
public class TunnelDailyReportHandlerTest {

    @Test
    public void testColumnWidthConfiguration() {
        // 创建测试数据
        List<PatrolAssetCheck> reportDataList = createTestData();

        // 创建处理器
        TunnelDailyReportHandler handler = new TunnelDailyReportHandler(reportDataList, new HashMap<>());

        // 验证处理器创建成功
        assertNotNull(handler, "TunnelDailyReportHandler应该创建成功");

        // 测试长文本是否需要换行的逻辑
        testTextWrappingLogic();
    }

    @Test
    public void testTextWrappingLogic() {
        // 测试不同长度的文本
        String shortText = "洞口"; // 2个中文字符 = 4个宽度单位
        String mediumText = "洞门及翼墙结构"; // 7个中文字符 = 14个宽度单位
        String longText = "洞顶预埋件和悬吊件"; // 8个中文字符 = 16个宽度单位

        // 模拟列宽设置
        int oldColumnWidth = 13; // 原来的B列宽度
        int newColumnWidth = 18; // 新的B列宽度

        // 计算有效长度
        int shortTextLength = calculateEffectiveLength(shortText);
        int mediumTextLength = calculateEffectiveLength(mediumText);
        int longTextLength = calculateEffectiveLength(longText);

        System.out.println("短文本 '" + shortText + "' 有效长度: " + shortTextLength);
        System.out.println("中等文本 '" + mediumText + "' 有效长度: " + mediumTextLength);
        System.out.println("长文本 '" + longText + "' 有效长度: " + longTextLength);

        // 验证在旧列宽下是否需要换行
        assertTrue(longTextLength > oldColumnWidth, "长文本在旧列宽(" + oldColumnWidth + ")下应该需要换行");

        // 验证在新列宽下是否不需要换行
        assertFalse(longTextLength > newColumnWidth, "长文本在新列宽(" + newColumnWidth + ")下应该不需要换行");

        System.out.println("测试通过：列宽从 " + oldColumnWidth + " 增加到 " + newColumnWidth + " 可以容纳长文本");
    }

    private List<PatrolAssetCheck> createTestData() {
        List<PatrolAssetCheck> reportDataList = new ArrayList<>();

        PatrolAssetCheck reportData = new PatrolAssetCheck();
        reportData.setAssetName("测试隧道");
        reportData.setAssetCode("TUN001");
        reportData.setPropertyUnitName("测试管理单位");
        reportData.setMaintainUnitName("测试养护单位");
        reportData.setRouteCode("R001");
        reportData.setMaintenanceSectionName("测试路线");
        reportData.setCenterStake("K10+500");
        reportData.setCheckTime(new Date());
        reportData.setWeather("晴");

        // 创建检查详情，包含长名称
        List<PatrolAssetCheckDetail> details = new ArrayList<>();

        PatrolAssetCheckDetail detail1 = new PatrolAssetCheckDetail();
        detail1.setPartsTypeName("洞顶预埋件和悬吊件"); // 这是一个长名称
        detail1.setDes("检查洞顶预埋件和悬吊件的安装情况");
        detail1.setDefect("未见异常");
        detail1.setAdvice("情况正常");
        details.add(detail1);

        reportData.setPatrolCheckDetailList(details);
        reportDataList.add(reportData);

        return reportDataList;
    }

    /**
     * 计算文本的有效长度（模拟AbstractExcelReportHandler中的逻辑）
     */
    private int calculateEffectiveLength(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        int effectiveLength = 0;
        for (char c : text.toCharArray()) {
            if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ||
                c > 0x3000) {
                effectiveLength += 2;  // 中文字符计为2个宽度单位
            } else {
                effectiveLength += 1;  // 英文字符计为1个宽度单位
            }
        }
        return effectiveLength;
    }
}
