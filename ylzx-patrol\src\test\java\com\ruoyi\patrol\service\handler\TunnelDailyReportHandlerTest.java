package com.ruoyi.patrol.service.handler;

import cn.idev.excel.EasyExcel;
import com.ruoyi.patrol.domain.PatrolAssetCheck;
import com.ruoyi.patrol.domain.PatrolAssetCheckDetail;
import com.ruoyi.patrol.service.handler.impl.TunnelDailyReportHandler;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Path;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TunnelDailyReportHandler的测试类，特别测试自动换行功能
 */
public class TunnelDailyReportHandlerTest {

    @TempDir
    Path tempDir;

    @Test
    public void testLongPartsTypeNameWrapping() throws IOException {
        // 创建测试数据
        List<PatrolAssetCheck> reportDataList = createTestData();
        
        // 创建临时文件
        Path excelFile = tempDir.resolve("tunnel_daily_report_test.xlsx");
        
        // 生成Excel文件
        try (FileOutputStream outputStream = new FileOutputStream(excelFile.toFile())) {
            EasyExcel.write(outputStream)
                .registerWriteHandler(new TunnelDailyReportHandler(reportDataList, new HashMap<>()))
                .sheet("隧道日常检查记录表")
                .doWrite(new ArrayList<>());
        }
        
        // 验证生成的Excel文件
        verifyExcelFile(excelFile);
    }
    
    private List<PatrolAssetCheck> createTestData() {
        List<PatrolAssetCheck> reportDataList = new ArrayList<>();
        
        PatrolAssetCheck reportData = new PatrolAssetCheck();
        reportData.setAssetName("测试隧道");
        reportData.setAssetCode("TUN001");
        reportData.setPropertyUnitName("测试管理单位");
        reportData.setMaintainUnitName("测试养护单位");
        reportData.setRouteCode("R001");
        reportData.setMaintenanceSectionName("测试路线");
        reportData.setCenterStake("K10+500");
        reportData.setCheckTime(new Date());
        reportData.setWeather("晴");
        
        // 创建检查详情，包含长名称
        List<PatrolAssetCheckDetail> details = new ArrayList<>();
        
        PatrolAssetCheckDetail detail1 = new PatrolAssetCheckDetail();
        detail1.setPartsTypeName("洞顶预埋件和悬吊件"); // 这是一个长名称，应该会换行
        detail1.setDes("检查洞顶预埋件和悬吊件的安装情况，确保无松动、无锈蚀、无变形等问题");
        detail1.setDefect("未见异常");
        detail1.setAdvice("情况正常");
        details.add(detail1);
        
        PatrolAssetCheckDetail detail2 = new PatrolAssetCheckDetail();
        detail2.setPartsTypeName("洞门及翼墙结构"); // 另一个测试名称
        detail2.setDes("检查洞门及翼墙结构的完整性");
        detail2.setDefect("未见异常");
        detail2.setAdvice("情况正常");
        details.add(detail2);
        
        reportData.setPatrolCheckDetailList(details);
        reportDataList.add(reportData);
        
        return reportDataList;
    }
    
    private void verifyExcelFile(Path excelFile) throws IOException {
        try (FileInputStream inputStream = new FileInputStream(excelFile.toFile());
             Workbook workbook = new XSSFWorkbook(inputStream)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            assertNotNull(sheet, "工作表应该存在");
            
            // 查找包含"洞顶预埋件和悬吊件"的单元格
            Cell targetCell = findCellWithText(sheet, "洞顶预埋件和悬吊件");
            assertNotNull(targetCell, "应该找到包含'洞顶预埋件和悬吊件'的单元格");
            
            // 验证单元格样式是否设置了自动换行
            CellStyle cellStyle = targetCell.getCellStyle();
            assertTrue(cellStyle.getWrapText(), "单元格应该设置了自动换行");
            
            // 验证单元格对齐方式
            assertEquals(HorizontalAlignment.LEFT, cellStyle.getAlignment(), "单元格应该是左对齐");
            
            System.out.println("测试通过：长文本单元格已正确设置自动换行");
            System.out.println("单元格内容: " + targetCell.getStringCellValue());
            System.out.println("自动换行设置: " + cellStyle.getWrapText());
            System.out.println("对齐方式: " + cellStyle.getAlignment());
        }
    }
    
    private Cell findCellWithText(Sheet sheet, String searchText) {
        for (Row row : sheet) {
            for (Cell cell : row) {
                if (cell.getCellType() == CellType.STRING) {
                    String cellValue = cell.getStringCellValue();
                    if (cellValue != null && cellValue.contains(searchText)) {
                        return cell;
                    }
                }
            }
        }
        return null;
    }
}
