package com.ruoyi.patrol.service.handler;

/**
 * TunnelDailyReportHandler的测试类，特别测试文本换行逻辑
 */
public class TunnelDailyReportHandlerTest {

    public static void main(String[] args) {
        testTextWrappingLogic();
    }

    public static void testTextWrappingLogic() {
        // 测试不同长度的文本
        String shortText = "洞口"; // 2个中文字符 = 4个宽度单位
        String mediumText = "洞门及翼墙结构"; // 7个中文字符 = 14个宽度单位
        String longText = "洞顶预埋件和悬吊件"; // 8个中文字符 = 16个宽度单位

        // 模拟列宽设置
        int columnWidth = 13; // B列宽度

        // 计算有效长度
        int shortTextLength = calculateEffectiveLength(shortText);
        int mediumTextLength = calculateEffectiveLength(mediumText);
        int longTextLength = calculateEffectiveLength(longText);

        System.out.println("=== 文本换行分析 ===");
        System.out.println("B列宽度设置: " + columnWidth + " 个字符");
        System.out.println();

        System.out.println("短文本 '" + shortText + "' 有效长度: " + shortTextLength +
                          " -> " + (shortTextLength > columnWidth ? "需要换行" : "不需要换行"));
        System.out.println("中等文本 '" + mediumText + "' 有效长度: " + mediumTextLength +
                          " -> " + (mediumTextLength > columnWidth ? "需要换行" : "不需要换行"));
        System.out.println("长文本 '" + longText + "' 有效长度: " + longTextLength +
                          " -> " + (longTextLength > columnWidth ? "需要换行" : "不需要换行"));

        System.out.println();
        System.out.println("=== 结论 ===");
        if (longTextLength > columnWidth) {
            System.out.println("'" + longText + "' 应该会换行，因为 " + longTextLength + " > " + columnWidth);
        } else {
            System.out.println("'" + longText + "' 不会换行，因为 " + longTextLength + " <= " + columnWidth);
        }
    }

    /**
     * 计算文本的有效长度（模拟AbstractExcelReportHandler中的逻辑）
     */
    public static int calculateEffectiveLength(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        int effectiveLength = 0;
        for (char c : text.toCharArray()) {
            if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS ||
                c > 0x3000) {
                effectiveLength += 2;  // 中文字符计为2个宽度单位
            } else {
                effectiveLength += 1;  // 英文字符计为1个宽度单位
            }
        }
        return effectiveLength;
    }
}
